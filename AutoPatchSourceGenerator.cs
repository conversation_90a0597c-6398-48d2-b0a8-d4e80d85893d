// AutoPatchSourceGenerator.cs
// <auto-generated>
// Source generator that reads .autopatch files containing lines like:
//   Il2Cpp.MinimapFogOfWar.Initialize -> Postfix
// For each line, it discovers ALL overloads via R<PERSON>lyn, then emits:
//   - AutoPatchEvents with typed events per overload
//   - Harmony patch classes with [HarmonyPatch(typeof(...), "Method", typeof(...), ...)]
// No TargetMethod(), no object[] packing, fully typed.
// </auto-generated>

#nullable enable

using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;

[Generator]
public sealed class AutoPatchSourceGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // All .autopatch files
        var autopatchTexts =
            context.AdditionalTextsProvider
                   .Where(f => f.Path.EndsWith(".autopatch", StringComparison.OrdinalIgnoreCase))
                   .Select((file, ct) => file.GetText(ct)?.ToString() ?? string.Empty);

        // We need the Compilation to resolve types + all autopatch lines
        var combined = context.CompilationProvider.Combine(autopatchTexts.Collect());

        context.RegisterSourceOutput(combined, (spc, pair) =>
        {
            var (compilation, files) = pair;

            if (files.IsDefaultOrEmpty)
                return;

            var requests = new List<PatchRequest>();
            foreach (var content in files)
                requests.AddRange(ParsePatches(content));

            if (requests.Count == 0)
                return;

            // Resolve all overloads for each request
            var resolved = ResolveRequests(compilation, requests);

            if (resolved.Count == 0)
                return;

            spc.AddSource("AutoPatchEvents.g.cs", GenerateEvents(resolved));
            spc.AddSource("AutoPatches.g.cs", GenerateHarmonyPatches(resolved));
        });
    }

    // =========================
    // Parsing (.autopatch)
    // =========================
    private static List<PatchRequest> ParsePatches(string content)
    {
        var list = new List<PatchRequest>();
        if (string.IsNullOrWhiteSpace(content))
            return list;

        var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        foreach (var raw in lines)
        {
            var line = raw.Trim();
            if (line.Length == 0 || line.StartsWith("#")) continue;

            // Format: Namespace.Type.Method -> PatchType
            // Example: Il2Cpp.MinimapFogOfWar.Initialize -> Postfix
            var parts = line.Split(new[] { "->" }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2) continue;

            var left = parts[0].Trim();
            var patchType = parts[1].Trim();

            if (!IsSupportedPatchType(patchType)) continue;

            var lastDot = left.LastIndexOf('.');
            if (lastDot <= 0) continue;

            var className = left.Substring(0, lastDot).Trim();
            var methodName = left.Substring(lastDot + 1).Trim();
            if (className.Length == 0 || methodName.Length == 0) continue;

            list.Add(new PatchRequest
            {
                ClassName = className,
                MethodName = methodName,
                PatchType = patchType
            });
        }

        return list;
    }

    private static bool IsSupportedPatchType(string kind)
        => kind is "Prefix" or "Postfix" or "Finalizer";

    // =========================
    // Resolution (Roslyn)
    // =========================
    private static List<ResolvedOverload> ResolveRequests(Compilation compilation, List<PatchRequest> requests)
    {
        var result = new List<ResolvedOverload>();

        foreach (var req in requests)
        {
            var type = compilation.GetTypeByMetadataName(req.ClassName);
            if (type is null)
                continue; // Type not found in references, skip silently

            // All methods with this name (overloads)
            var methods = type.GetMembers()
                              .OfType<IMethodSymbol>()
                              .Where(m => m.Name == req.MethodName && m.MethodKind == MethodKind.Ordinary)
                              .ToList();

            if (methods.Count == 0)
                continue;

            // Emit one patch per overload
            for (int i = 0; i < methods.Count; i++)
            {
                var m = methods[i];

                // Build a stable, readable suffix for overload (e.g., _ovl2, _ovl3, or with param count)
                var overloadSuffix = $"_ovl{i + 1}";

                result.Add(new ResolvedOverload
                {
                    Request = req,
                    OverloadIndex = i + 1,
                    ContainingType = type,
                    Method = m,
                    OverloadSuffix = overloadSuffix
                });
            }
        }

        return result;
    }

    // =========================
    // Codegen - Events
    // =========================
    private static SourceText GenerateEvents(List<ResolvedOverload> overloads)
    {
        var sb = new StringBuilder();
        sb.AppendLine("// <auto-generated/>");
        sb.AppendLine("using System;");
        sb.AppendLine();

        sb.AppendLine("public static class AutoPatchEvents");
        sb.AppendLine("{");

        foreach (var ro in overloads)
        {
            var req = ro.Request;
            var m = ro.Method;

            // Build event type parameters:
            // instance (if instance method), then each method param (by value), then result (postfix only & non-void), then Exception (finalizer)
            var evtParamTypes = new List<string>();

            if (!m.IsStatic)
                evtParamTypes.Add(DisplayType(ro.ContainingType, fullyQualified: true));

            foreach (var p in m.Parameters)
                evtParamTypes.Add(DisplayType(UnderlyingTypeForEvent(p), fullyQualified: true));

            if (req.PatchType == "Postfix" && !m.ReturnsVoid)
                evtParamTypes.Add(DisplayType(m.ReturnType, fullyQualified: true));

            if (req.PatchType == "Finalizer")
                evtParamTypes.Add("System.Exception");

            var actionType = evtParamTypes.Count == 0
                ? "System.Action"
                : $"System.Action<{string.Join(", ", evtParamTypes)}>";

            var eventName = $"On{req.MethodName}{req.PatchType}{ro.OverloadSuffix}";
            sb.AppendLine($"    public static event {actionType} {eventName};");

            // Fire method (same parameter types, with names)
            var fireParams = new List<(string Type, string Name)>();
            if (!m.IsStatic)
                fireParams.Add((DisplayType(ro.ContainingType, fullyQualified: true), "instance"));

            foreach (var p in m.Parameters)
                fireParams.Add((DisplayType(UnderlyingTypeForEvent(p), fullyQualified: true), p.Name));

            if (req.PatchType == "Postfix" && !m.ReturnsVoid)
                fireParams.Add((DisplayType(m.ReturnType, fullyQualified: true), "result"));

            if (req.PatchType == "Finalizer")
                fireParams.Add(("System.Exception", "ex"));

            sb.Append("    internal static void Fire").Append(eventName).Append('(');
            sb.Append(string.Join(", ", fireParams.Select(t => $"{t.Type} {t.Name}")));
            sb.AppendLine(")");
            sb.Append("        => ").Append(eventName).Append("?.Invoke(");
            sb.Append(string.Join(", ", fireParams.Select(t => t.Name)));
            sb.AppendLine(");");
            sb.AppendLine();
        }

        sb.AppendLine("}");
        return SourceText.From(sb.ToString(), Encoding.UTF8);
    }

    // =========================
    // Codegen - Harmony Patches
    // =========================
    private static SourceText GenerateHarmonyPatches(List<ResolvedOverload> overloads)
    {
        var sb = new StringBuilder();
        sb.AppendLine("// <auto-generated/>");
        sb.AppendLine("using System;");
        sb.AppendLine("using HarmonyLib;");
        sb.AppendLine();

        foreach (var ro in overloads)
        {
            var req = ro.Request;
            var m = ro.Method;
            var typeFqn = DisplayType(ro.ContainingType, fullyQualified: true);

            // Class name for patch
            var patchClassName = SanitizeIdentifier($"{ro.ContainingType.Name}_{m.Name}_{req.PatchType}{ro.OverloadSuffix}_Patch");

            // Build the HarmonyPatch attributes
            sb.AppendLine($"[HarmonyPatch(typeof({typeFqn}))]");

            // typeof(...) list for parameters
            var typeofParams = string.Join(", ", m.Parameters.Select(p => $"typeof({DisplayType(p.Type, fullyQualified: true)})"));
            if (typeofParams.Length > 0)
                sb.AppendLine($"[HarmonyPatch(\"{m.Name}\", {typeofParams})]");
            else
                sb.AppendLine($"[HarmonyPatch(\"{m.Name}\")]");

            sb.AppendLine($"public static class {patchClassName}");
            sb.AppendLine("{");

            // Build method parameter list for the patch method (with ref/out/in modifiers)
            var patchParams = new List<string>();
            if (!m.IsStatic)
                patchParams.Add($"{typeFqn} __instance");

            foreach (var p in m.Parameters)
            {
                var mod = ParamModifier(p.RefKind); // ref/out/in/none
                var pType = DisplayType(UnderlyingTypeForPatchSignature(p), fullyQualified: true);
                patchParams.Add($"{mod}{pType} {p.Name}".Trim());
            }

            switch (req.PatchType)
            {
                case "Prefix":
                {
                    sb.AppendLine("    [HarmonyPrefix]");
                    sb.AppendLine($"    private static void Prefix({string.Join(", ", patchParams)})");
                    sb.AppendLine("    {");
                    var fireName = $"AutoPatchEvents.FireOn{req.MethodName}Prefix{ro.OverloadSuffix}";
                    var args = new List<string>();
                    if (!m.IsStatic) args.Add("__instance");
                    args.AddRange(m.Parameters.Select(p => p.Name));
                    sb.AppendLine($"        {fireName}({string.Join(", ", args)});");
                    sb.AppendLine("    }");
                    break;
                }

                case "Postfix":
                {
                    // Include __result if non-void
                    var localParams = new List<string>(patchParams);
                    if (!m.ReturnsVoid)
                        localParams.Add($"{DisplayType(m.ReturnType, fullyQualified: true)} __result");

                    sb.AppendLine("    [HarmonyPostfix]");
                    sb.AppendLine($"    private static void Postfix({string.Join(", ", localParams)})");
                    sb.AppendLine("    {");
                    var fireName = $"AutoPatchEvents.FireOn{req.MethodName}Postfix{ro.OverloadSuffix}";
                    var args = new List<string>();
                    if (!m.IsStatic) args.Add("__instance");
                    args.AddRange(m.Parameters.Select(p => p.Name));
                    if (!m.ReturnsVoid) args.Add("__result");
                    sb.AppendLine($"        {fireName}({string.Join(", ", args)});");
                    sb.AppendLine("    }");
                    break;
                }

                case "Finalizer":
                {
                    var localParams = new List<string>(patchParams)
                    {
                        "System.Exception __exception"
                    };

                    sb.AppendLine("    [HarmonyFinalizer]");
                    sb.AppendLine($"    private static System.Exception Finalizer({string.Join(", ", localParams)})");
                    sb.AppendLine("    {");
                    var fireName = $"AutoPatchEvents.FireOn{req.MethodName}Finalizer{ro.OverloadSuffix}";
                    var args = new List<string>();
                    if (!m.IsStatic) args.Add("__instance");
                    args.AddRange(m.Parameters.Select(p => p.Name));
                    args.Add("__exception");
                    sb.AppendLine($"        {fireName}({string.Join(", ", args)});");
                    sb.AppendLine("        return __exception;"); // pass-through behavior
                    sb.AppendLine("    }");
                    break;
                }
            }

            sb.AppendLine("}");
            sb.AppendLine();
        }

        return SourceText.From(sb.ToString(), Encoding.UTF8);
    }

    // =========================
    // Helpers
    // =========================

    // For events we want by-value types (no ref/out/in), so use the element type if by-ref.
    private static ITypeSymbol UnderlyingTypeForEvent(IParameterSymbol p)
    {
        // Roslyn models ref/out as RefKind + the same Type; Action<T> can't use ref/out anyway.
        // We keep the declared type (which is the element type) for event delegates.
        return p.Type;
    }

    // For patch method signatures we keep the ref/out/in modifier, but the *type* is still the declared type.
    // Roslyn already exposes the element as Type; the ref-ness is in RefKind.
    private static ITypeSymbol UnderlyingTypeForPatchSignature(IParameterSymbol p) => p.Type;

    private static string ParamModifier(RefKind rk) => rk switch
    {
        RefKind.None => string.Empty,
        RefKind.Ref  => "ref ",
        RefKind.Out  => "out ",
        RefKind.In   => "in ",
        _ => string.Empty
    };

    private static string DisplayType(ITypeSymbol t, bool fullyQualified)
    {
        var fmt = fullyQualified
            ? SymbolDisplayFormat.FullyQualifiedFormat
            : SymbolDisplayFormat.MinimallyQualifiedFormat;

        // Important: FullyQualifiedFormat yields "global::Ns.Type" which is valid in typeof(...) and signatures.
        return t.ToDisplayString(fmt);
    }

    private static string SanitizeIdentifier(string raw)
    {
        if (string.IsNullOrEmpty(raw)) return "_GeneratedPatch";
        var sb = new StringBuilder(raw.Length);
        foreach (var ch in raw)
            sb.Append(char.IsLetterOrDigit(ch) || ch == '_' ? ch : '_');
        if (char.IsDigit(sb[0])) sb.Insert(0, '_');
        return sb.ToString();
    }

    // =========================
    // Models
    // =========================
    private sealed class PatchRequest
    {
        public string ClassName { get; set; } = "";
        public string MethodName { get; set; } = "";
        public string PatchType { get; set; } = ""; // Prefix|Postfix|Finalizer
    }

    private sealed class ResolvedOverload
    {
        public PatchRequest Request { get; set; } = default!;
        public INamedTypeSymbol ContainingType { get; set; } = default!;
        public IMethodSymbol Method { get; set; } = default!;
        public int OverloadIndex { get; set; }
        public string OverloadSuffix { get; set; } = ""; // e.g., "_ovl1"
    }
}
